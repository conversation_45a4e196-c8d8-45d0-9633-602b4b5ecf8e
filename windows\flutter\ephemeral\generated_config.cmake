# Generated code do not commit.
file(TO_CMAKE_PATH "K:\\Downloads\\flutter_windows_3.16.2-stable\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "K:\\flutter_projects\\Frontend" PROJECT_DIR)

set(FLUTTER_VERSION "2.1.7+50" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 2 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 1 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 7 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 50 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=K:\\Downloads\\flutter_windows_3.16.2-stable\\flutter"
  "PROJECT_DIR=K:\\flutter_projects\\Frontend"
  "FLUTTER_ROOT=K:\\Downloads\\flutter_windows_3.16.2-stable\\flutter"
  "FLUTTER_EPHEMERAL_DIR=K:\\flutter_projects\\Frontend\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=K:\\flutter_projects\\Frontend"
  "FLUTTER_TARGET=K:\\flutter_projects\\Frontend\\lib\\main_prod.dart"
  "DART_DEFINES=RkxVVFRFUl9XRUJfQVVUT19ERVRFQ1Q9dHJ1ZQ==,RkxVVFRFUl9XRUJfQ0FOVkFTS0lUX1VSTD1odHRwczovL3d3dy5nc3RhdGljLmNvbS9mbHV0dGVyLWNhbnZhc2tpdC9jZjdhOWQwODAwZjJhNWRhMTY2ZGJlMGViOWZiMjQ3NjAxODI2OWIxLw==,RkxVVFRFUl9BUFBfRkxBVk9SPXByb2Q="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=K:\\flutter_projects\\Frontend\\.dart_tool\\package_config.json"
)
